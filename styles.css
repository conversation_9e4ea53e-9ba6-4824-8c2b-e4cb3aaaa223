/* Clinical Trial RAG Application - Professional Styling */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* Root Variables - Professional Healthcare Color Scheme */
:root {
    --primary-blue: #2E86AB;
    --secondary-blue: #A23B72;
    --accent-teal: #F18F01;
    --light-blue: #E8F4FD;
    --dark-blue: #1B4D72;
    --success-green: #28A745;
    --warning-orange: #FD7E14;
    --error-red: #DC3545;
    --text-primary: #2C3E50;
    --text-secondary: #6C757D;
    --background-light: #F8FAFC;
    --border-light: #E2E8F0;
    --shadow-light: 0 2px 4px rgba(0,0,0,0.1);
    --shadow-medium: 0 4px 12px rgba(0,0,0,0.15);
    --border-radius: 12px;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Global Styles */
.stApp {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
    background: #000000;
    min-height: 100vh;
}

/* Streamlit Navigation and UI Elements */
.stApp > header {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

/* Streamlit sidebar */
.css-1d391kg {
    background: rgba(255, 255, 255, 0.05) !important;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
}

/* Streamlit main content area */
.main .block-container {
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
    padding: 2rem;
    margin-top: 1rem;
}

/* Streamlit text elements */
.stMarkdown, .stText {
    color: #ffffff !important;
}

/* Streamlit buttons */
.stButton > button {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.stButton > button:hover {
    background: rgba(255, 255, 255, 0.2) !important;
    border: 1px solid rgba(255, 255, 255, 0.4) !important;
}

/* Streamlit Settings Menu and Navigation */
[data-testid="stToolbar"] {
    background: rgba(255, 255, 255, 0.1) !important;
    backdrop-filter: blur(10px);
}

[data-testid="stHeader"] {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(10px);
}

/* Settings button */
[data-testid="stToolbar"] button {
    color: #ffffff !important;
    background: rgba(255, 255, 255, 0.1) !important;
}

[data-testid="stToolbar"] button:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

/* Menu items */
[data-testid="stSidebar"] {
    background: rgba(255, 255, 255, 0.05) !important;
}

/* Input fields */
.stTextInput > div > div > input {
    background: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* File uploader */
[data-testid="stFileUploader"] {
    background: rgba(255, 255, 255, 0.05) !important;
    border: 2px dashed rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
}

/* Progress bars */
.stProgress > div > div {
    background: rgba(255, 255, 255, 0.2) !important;
}

/* Hide Streamlit Default Elements */
#MainMenu {visibility: hidden;}
footer {visibility: hidden;}
header {visibility: hidden;}
.stDeployButton {display: none;}

/* Professional Header Styling */
.main-header-container {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    padding: 2rem 0;
    margin: -1rem -1rem 2rem -1rem;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--shadow-medium);
    position: relative;
    overflow: hidden;
}

.main-header-container::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    opacity: 0.3;
}

.header-content {
    position: relative;
    z-index: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 2rem;
}

.logo-container {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 50%;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.logo-container:hover {
    transform: scale(1.05);
    background: rgba(255, 255, 255, 0.2);
}

.clinical-logo {
    font-size: 2.5rem;
    color: white;
    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.header-text {
    text-align: center;
    color: white;
}

.main-title {
    font-size: 3rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0,0,0,0.2);
    letter-spacing: -0.02em;
}

.subtitle {
    font-size: 1.2rem;
    font-weight: 400;
    margin: 0.5rem 0 0 0;
    opacity: 0.9;
    letter-spacing: 0.01em;
}

.tagline {
    font-size: 1rem;
    font-weight: 300;
    margin: 0.25rem 0 0 0;
    opacity: 0.8;
    font-style: italic;
}

/* Card Styling */
.stContainer > div {
    background: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-light);
    transition: var(--transition);
}

.stContainer > div:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
}

/* Upload Section Styling */
.upload-section {
    background: white;
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-light);
    border: 2px dashed var(--border-light);
    transition: var(--transition);
    margin-bottom: 2rem;
}

.upload-section:hover {
    border-color: var(--primary-blue);
    box-shadow: var(--shadow-medium);
}

/* Button Styling */
.stButton > button {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
    color: white;
    border: none;
    border-radius: var(--border-radius);
    padding: 0.75rem 2rem;
    font-weight: 600;
    font-size: 1rem;
    transition: var(--transition);
    box-shadow: var(--shadow-light);
    text-transform: none;
    letter-spacing: 0.01em;
}

.stButton > button:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.stButton > button:active {
    transform: translateY(0);
}

/* Progress Bar Styling */
.stProgress > div > div > div {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border-radius: var(--border-radius);
    height: 8px;
}

/* Metric Styling */
.metric-container {
    background: var(--background-light);
    padding: 1.5rem;
    border-radius: var(--border-radius);
    border: 1px solid var(--border-light);
    text-align: center;
    transition: var(--transition);
}

.metric-container:hover {
    background: white;
    box-shadow: var(--shadow-light);
}

/* Sidebar Styling */
.css-1d391kg {
    background: white;
    border-right: 1px solid var(--border-light);
}

/* Expander Styling */
.streamlit-expanderHeader {
    background: var(--background-light);
    border-radius: var(--border-radius);
    border: 1px solid var(--border-light);
    transition: var(--transition);
}

.streamlit-expanderHeader:hover {
    background: white;
    box-shadow: var(--shadow-light);
}

/* Text Input Styling */
.stTextInput > div > div > input {
    border-radius: var(--border-radius);
    border: 2px solid var(--border-light);
    transition: var(--transition);
    font-size: 1rem;
    padding: 0.75rem;
}

.stTextInput > div > div > input:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(46, 134, 171, 0.1);
}

/* Success/Error Message Styling */
.stSuccess {
    background: rgba(40, 167, 69, 0.1);
    border: 1px solid var(--success-green);
    border-radius: var(--border-radius);
    color: var(--success-green);
}

.stError {
    background: rgba(220, 53, 69, 0.1);
    border: 1px solid var(--error-red);
    border-radius: var(--border-radius);
    color: var(--error-red);
}

.stWarning {
    background: rgba(253, 126, 20, 0.1);
    border: 1px solid var(--warning-orange);
    border-radius: var(--border-radius);
    color: var(--warning-orange);
}

/* Responsive Design */
@media (max-width: 768px) {
    .header-content {
        flex-direction: column;
        gap: 1rem;
    }
    
    .main-title {
        font-size: 2rem;
    }
    
    .subtitle {
        font-size: 1rem;
    }
    
    .logo-container {
        width: 60px;
        height: 60px;
    }
    
    .clinical-logo {
        font-size: 2rem;
    }
}

/* Suggested Prompts Styling */
.suggested-prompts-container {
    background: var(--background-light);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1rem 0;
    border: 1px solid var(--border-light);
}

.prompt-button {
    background: white;
    border: 2px solid var(--border-light);
    border-radius: var(--border-radius);
    padding: 1rem;
    margin: 0.5rem 0;
    transition: var(--transition);
    cursor: pointer;
    text-align: left;
    width: 100%;
    font-size: 0.9rem;
    line-height: 1.4;
}

.prompt-button:hover {
    border-color: var(--primary-blue);
    background: var(--light-blue);
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.prompt-button:active {
    transform: translateY(0);
}

.prompt-category {
    font-size: 0.75rem;
    color: var(--text-secondary);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    margin-bottom: 0.25rem;
}

/* Knowledge Base Status */
.knowledge-base-status {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: white;
    padding: 1rem;
    border-radius: var(--border-radius);
    text-align: center;
    margin: 1rem 0;
    box-shadow: var(--shadow-light);
}

/* Enhanced File Upload Area */
.upload-area {
    border: 3px dashed var(--border-light);
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    background: var(--background-light);
    transition: var(--transition);
    margin: 1rem 0;
}

.upload-area:hover {
    border-color: var(--primary-blue);
    background: white;
}

.upload-area.dragover {
    border-color: var(--accent-teal);
    background: rgba(241, 143, 1, 0.1);
}

/* Processing Status */
.processing-status {
    background: white;
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin: 1rem 0;
    border-left: 4px solid var(--primary-blue);
    box-shadow: var(--shadow-light);
}

.processing-step {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 0.5rem 0;
}

.processing-step.completed {
    color: var(--success-green);
}

.processing-step.active {
    color: var(--primary-blue);
    font-weight: 600;
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.6s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-up {
    animation: slideUp 0.4s ease-out;
}

@keyframes slideUp {
    from { opacity: 0; transform: translateY(30px); }
    to { opacity: 1; transform: translateY(0); }
}

.pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.7; }
    100% { opacity: 1; }
}
