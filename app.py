"""
Clinical Trial RAG Application
A comprehensive RAG system for processing and querying clinical trial documents
"""

import streamlit as st
import uuid
import time
from typing import List, Dict, Optional

# Import custom modules
from document_processor import DocumentProcessor, validate_pdf_file
from embedding_generator import get_embedding_generator
from vector_database import get_vector_database
from groq_integration import get_groq_client

# Page configuration
st.set_page_config(
    page_title="Clinical Trial RAG System",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better UI
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .upload-section {
        border: 2px dashed #cccccc;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        margin: 1rem 0;
    }
    .chunk-display {
        background-color: #f8f9fa;
        border-left: 4px solid #1f77b4;
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 5px;
    }
    .similarity-score {
        background-color: #e3f2fd;
        color: #1565c0;
        padding: 0.2rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize session state variables"""
    if 'processed_documents' not in st.session_state:
        st.session_state.processed_documents = {}
    if 'current_document_id' not in st.session_state:
        st.session_state.current_document_id = None
    if 'query_history' not in st.session_state:
        st.session_state.query_history = []


def display_header():
    """Display the main header"""
    st.markdown('<h1 class="main-header">🏥 Clinical Trial RAG System</h1>', unsafe_allow_html=True)
    st.markdown("---")


def upload_and_process_document():
    """Handle document upload and processing"""
    st.subheader("📄 Document Upload & Processing")

    # File uploader with drag and drop
    uploaded_file = st.file_uploader(
        "Drag and drop your clinical trial PDF document here",
        type=['pdf'],
        help="Upload a PDF document containing clinical trial information"
    )

    if uploaded_file is not None:
        # Validate PDF file
        if not validate_pdf_file(uploaded_file):
            st.error("❌ Invalid PDF file. Please upload a valid PDF document.")
            return

        # Display file info
        st.success(f"✅ File uploaded: {uploaded_file.name}")
        st.info(f"📊 File size: {uploaded_file.size / 1024:.1f} KB")

        # Process document button
        if st.button("🔄 Process Document", type="primary"):
            process_document(uploaded_file)


def process_document(uploaded_file):
    """Process the uploaded document"""
    try:
        with st.spinner("Processing document..."):
            # Initialize processors
            doc_processor = DocumentProcessor()
            embedding_gen = get_embedding_generator()
            vector_db = get_vector_database()

            # Process document
            progress_bar = st.progress(0)
            st.text("Extracting text from PDF...")

            document = doc_processor.process_document(uploaded_file, uploaded_file.name)
            progress_bar.progress(25)

            st.text("Generating embeddings...")
            chunks_with_embeddings = embedding_gen.generate_chunk_embeddings(document['chunks'])
            progress_bar.progress(50)

            st.text("Storing in vector database...")
            document_id = str(uuid.uuid4())
            vector_db.store_document_chunks(
                chunks_with_embeddings,
                document_id,
                uploaded_file.name
            )
            progress_bar.progress(75)

            # Store in session state
            st.session_state.processed_documents[document_id] = {
                'filename': uploaded_file.name,
                'chunks': chunks_with_embeddings,
                'metadata': {
                    'total_text_length': document['total_text_length'],
                    'num_chunks': document['num_chunks'],
                    'processed_at': time.strftime("%Y-%m-%d %H:%M:%S")
                }
            }
            st.session_state.current_document_id = document_id
            progress_bar.progress(100)

            st.success(f"✅ Document processed successfully!")
            st.balloons()

            # Display processing results
            display_processing_results(document)

    except Exception as e:
        st.error(f"❌ Error processing document: {str(e)}")


def display_processing_results(document):
    """Display document processing results"""
    st.subheader("📊 Processing Results")

    col1, col2, col3 = st.columns(3)

    with col1:
        st.metric("Total Text Length", f"{document['total_text_length']:,} chars")

    with col2:
        st.metric("Number of Chunks", document['num_chunks'])

    with col3:
        avg_chunk_length = document['total_text_length'] // document['num_chunks'] if document['num_chunks'] > 0 else 0
        st.metric("Avg Chunk Length", f"{avg_chunk_length} chars")

    # Show first few chunks as preview
    st.subheader("📝 Document Preview (First 3 Chunks)")
    for i, chunk in enumerate(document['chunks'][:3]):
        with st.expander(f"Chunk {i+1} ({chunk['length']} characters)"):
            st.text(chunk['text'][:500] + "..." if len(chunk['text']) > 500 else chunk['text'])


def query_interface():
    """Handle user queries and responses"""
    st.subheader("🔍 Query Interface")

    if not st.session_state.processed_documents:
        st.warning("⚠️ Please upload and process a document first.")
        return

    # Document selector
    if len(st.session_state.processed_documents) > 1:
        doc_options = {doc_id: doc_data['filename']
                      for doc_id, doc_data in st.session_state.processed_documents.items()}
        selected_doc_id = st.selectbox(
            "Select document to query:",
            options=list(doc_options.keys()),
            format_func=lambda x: doc_options[x],
            index=0
        )
        st.session_state.current_document_id = selected_doc_id

    # Query input
    query = st.text_input(
        "Enter your question about the clinical trial:",
        placeholder="e.g., What were the primary endpoints of this study?",
        help="Ask questions about study design, endpoints, results, safety, etc."
    )

    # Query parameters
    with st.expander("🔧 Advanced Settings"):
        col1, col2 = st.columns(2)
        with col1:
            top_k = st.slider("Number of relevant chunks to retrieve", 1, 10, 5)
            temperature = st.slider("Response creativity (temperature)", 0.0, 1.0, 0.3, 0.1)
        with col2:
            max_tokens = st.slider("Maximum response length", 256, 2048, 1024, 128)
            score_threshold = st.slider("Similarity threshold", 0.0, 1.0, 0.0, 0.1)

    # Process query
    if st.button("🚀 Ask Question", type="primary") and query:
        process_query(query, top_k, temperature, max_tokens, score_threshold)


def process_query(query: str, top_k: int, temperature: float, max_tokens: int, score_threshold: float):
    """Process user query and generate response"""
    try:
        with st.spinner("Searching for relevant information..."):
            # Initialize components
            embedding_gen = get_embedding_generator()
            vector_db = get_vector_database()
            groq_client = get_groq_client()

            # Generate query embedding
            query_embedding = embedding_gen.generate_query_embedding(query)

            # Search for similar chunks
            similar_chunks = vector_db.search_similar_chunks(
                query_embedding.tolist(),
                top_k=top_k,
                score_threshold=score_threshold
            )

            if not similar_chunks:
                st.warning("⚠️ No relevant information found for your query. Try rephrasing or lowering the similarity threshold.")
                return

            # Display retrieved chunks
            st.subheader("📚 Retrieved Relevant Information")
            for i, chunk in enumerate(similar_chunks):
                with st.expander(f"📄 Chunk {i+1} - Similarity: {chunk['score']:.3f}"):
                    st.markdown(f'<div class="chunk-display">{chunk["text"]}</div>', unsafe_allow_html=True)
                    st.caption(f"Source: {chunk['filename']} | Length: {chunk['length']} chars")

            # Generate response
            st.subheader("🤖 AI Response")
            with st.spinner("Generating response..."):
                response = groq_client.generate_response(
                    query,
                    similar_chunks,
                    temperature=temperature,
                    max_tokens=max_tokens
                )

                st.markdown(f"**Query:** {query}")
                st.markdown("**Answer:**")
                st.markdown(response)

                # Add to query history
                st.session_state.query_history.append({
                    'query': query,
                    'response': response,
                    'chunks_used': len(similar_chunks),
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S")
                })

    except Exception as e:
        st.error(f"❌ Error processing query: {str(e)}")


def sidebar_info():
    """Display sidebar information"""
    with st.sidebar:
        st.header("📋 System Information")

        # Document status
        st.subheader("📄 Processed Documents")
        if st.session_state.processed_documents:
            for doc_id, doc_data in st.session_state.processed_documents.items():
                st.write(f"• {doc_data['filename']}")
                st.caption(f"  Chunks: {doc_data['metadata']['num_chunks']}")
        else:
            st.write("No documents processed yet")

        st.markdown("---")

        # Query history
        st.subheader("🔍 Recent Queries")
        if st.session_state.query_history:
            for i, query_data in enumerate(reversed(st.session_state.query_history[-5:])):
                with st.expander(f"Query {len(st.session_state.query_history) - i}"):
                    st.write(f"**Q:** {query_data['query'][:100]}...")
                    st.caption(f"Time: {query_data['timestamp']}")
        else:
            st.write("No queries yet")

        st.markdown("---")

        # System info
        st.subheader("⚙️ Configuration")
        st.write("• **Embedding Model:** all-MiniLM-L6-v2")
        st.write("• **LLM Model:** Llama-4-Scout-17B")
        st.write("• **Vector DB:** Qdrant")
        st.write("• **Chunking:** Paragraph-based")


def main():
    """Main application function"""
    initialize_session_state()
    display_header()

    # Main content area
    col1, col2 = st.columns([2, 1])

    with col1:
        # Document upload section
        upload_and_process_document()

        st.markdown("---")

        # Query interface
        query_interface()

    with col2:
        # Sidebar information
        sidebar_info()


if __name__ == "__main__":
    main()