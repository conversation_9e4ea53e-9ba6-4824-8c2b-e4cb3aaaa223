"""
Clinical Trial RAG Application
A comprehensive RAG system for processing and querying clinical trial documents
"""

import streamlit as st
import uuid
import time
from typing import List, Dict, Optional

# Import custom modules
from document_processor import DocumentProcessor, validate_pdf_file
from embedding_generator import get_embedding_generator
from vector_database import get_vector_database
from groq_integration import get_groq_client
from prompt_suggestions import display_suggested_prompts

# Load custom CSS
def load_css():
    """Load custom CSS styling"""
    try:
        with open('styles.css', 'r') as f:
            css = f.read()
        st.markdown(f'<style>{css}</style>', unsafe_allow_html=True)
    except FileNotFoundError:
        st.warning("Custom CSS file not found. Using default styling.")

# Page configuration
st.set_page_config(
    page_title="Clinical Trial RAG System",
    page_icon="🏥",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better UI
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .upload-section {
        border: 2px dashed #cccccc;
        border-radius: 10px;
        padding: 2rem;
        text-align: center;
        margin: 1rem 0;
    }
    .chunk-display {
        background-color: #f8f9fa;
        border-left: 4px solid #1f77b4;
        padding: 1rem;
        margin: 0.5rem 0;
        border-radius: 5px;
    }
    .similarity-score {
        background-color: #e3f2fd;
        color: #1565c0;
        padding: 0.2rem 0.5rem;
        border-radius: 15px;
        font-size: 0.8rem;
        font-weight: bold;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize session state variables"""
    if 'processed_documents' not in st.session_state:
        st.session_state.processed_documents = {}
    if 'current_document_id' not in st.session_state:
        st.session_state.current_document_id = None
    if 'query_history' not in st.session_state:
        st.session_state.query_history = []


def display_header():
    """Display the professional header with logo and branding"""
    st.markdown("""
    <div class="main-header-container">
        <div class="header-content">
            <div class="logo-container">
                <div class="clinical-logo">🧬</div>
            </div>
            <div class="header-text">
                <h1 class="main-title">Clinical Trial RAG</h1>
                <p class="subtitle">Multi-Document Knowledge Base</p>
                <p class="tagline">Advanced AI-Powered Clinical Research Analysis</p>
            </div>
        </div>
    </div>
    """, unsafe_allow_html=True)


def upload_and_process_documents():
    """Handle batch document upload and processing"""
    st.subheader("📄 Batch Document Upload & Processing")

    # Multi-file uploader for batch processing
    uploaded_files = st.file_uploader(
        "Upload up to 10 clinical trial PDF documents",
        type=['pdf'],
        accept_multiple_files=True,
        help="Upload multiple PDF documents containing clinical trial information (max 10 files)"
    )

    if uploaded_files:
        # Limit to 10 files
        if len(uploaded_files) > 10:
            st.warning("⚠️ Maximum 10 files allowed. Only the first 10 files will be processed.")
            uploaded_files = uploaded_files[:10]

        # Display uploaded files info
        st.success(f"✅ {len(uploaded_files)} file(s) uploaded")

        # Show file details in expandable section
        with st.expander(f"📋 View uploaded files ({len(uploaded_files)})"):
            total_size = 0
            valid_files = []

            for i, file in enumerate(uploaded_files):
                col1, col2, col3 = st.columns([3, 1, 1])

                with col1:
                    st.write(f"📄 {file.name}")

                with col2:
                    file_size_kb = file.size / 1024
                    st.write(f"{file_size_kb:.1f} KB")
                    total_size += file_size_kb

                with col3:
                    if validate_pdf_file(file):
                        st.write("✅ Valid")
                        valid_files.append(file)
                    else:
                        st.write("❌ Invalid")

            st.info(f"📊 Total size: {total_size:.1f} KB | Valid files: {len(valid_files)}/{len(uploaded_files)}")

        # Process documents button
        if valid_files and st.button("🔄 Process All Documents", type="primary"):
            process_multiple_documents(valid_files)


def process_multiple_documents(uploaded_files):
    """Process multiple uploaded documents in batch"""
    try:
        st.subheader("🔄 Batch Processing Progress")

        # Initialize processors
        doc_processor = DocumentProcessor()
        embedding_gen = get_embedding_generator()
        vector_db = get_vector_database()

        # Overall progress tracking
        total_files = len(uploaded_files)
        overall_progress = st.progress(0)
        status_text = st.empty()

        # Individual file progress containers
        file_progress_containers = []
        for i, file in enumerate(uploaded_files):
            container = st.container()
            with container:
                st.write(f"📄 **{file.name}**")
                progress_bar = st.progress(0)
                status = st.empty()
            file_progress_containers.append((progress_bar, status))

        # Process each document
        processed_count = 0
        batch_results = []

        for i, uploaded_file in enumerate(uploaded_files):
            try:
                progress_bar, status = file_progress_containers[i]

                # Update overall status
                status_text.text(f"Processing file {i+1}/{total_files}: {uploaded_file.name}")

                # Extract text
                status.text("📖 Extracting text from PDF...")
                document = doc_processor.process_document(uploaded_file, uploaded_file.name)
                progress_bar.progress(25)

                # Generate embeddings
                status.text("🧠 Generating embeddings...")
                chunks_with_embeddings = embedding_gen.generate_chunk_embeddings(document['chunks'])
                progress_bar.progress(50)

                # Store in vector database with enhanced metadata
                status.text("🗄️ Storing in vector database...")
                document_id = str(uuid.uuid4())

                # Prepare enhanced metadata
                enhanced_metadata = {
                    'upload_timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'file_size': uploaded_file.size,
                    'total_text_length': document['total_text_length'],
                    'num_chunks': document['num_chunks']
                }

                vector_db.store_document_chunks(
                    chunks_with_embeddings,
                    document_id,
                    uploaded_file.name,
                    enhanced_metadata
                )
                progress_bar.progress(75)

                # Store in session state
                st.session_state.processed_documents[document_id] = {
                    'filename': uploaded_file.name,
                    'chunks': chunks_with_embeddings,
                    'metadata': {
                        'total_text_length': document['total_text_length'],
                        'num_chunks': document['num_chunks'],
                        'processed_at': time.strftime("%Y-%m-%d %H:%M:%S")
                    }
                }

                progress_bar.progress(100)
                status.text("✅ Completed successfully!")

                batch_results.append({
                    'filename': uploaded_file.name,
                    'document_id': document_id,
                    'success': True,
                    'metadata': document
                })

                processed_count += 1

            except Exception as e:
                progress_bar.progress(100)
                status.text(f"❌ Error: {str(e)[:50]}...")
                batch_results.append({
                    'filename': uploaded_file.name,
                    'success': False,
                    'error': str(e)
                })

            # Update overall progress
            overall_progress.progress((i + 1) / total_files)

        # Final status
        status_text.text(f"✅ Batch processing complete! {processed_count}/{total_files} files processed successfully.")

        if processed_count > 0:
            st.success(f"🎉 Successfully processed {processed_count} out of {total_files} documents!")
            display_batch_processing_results(batch_results)

    except Exception as e:
        st.error(f"❌ Error in batch processing: {str(e)}")


def process_document(uploaded_file):
    """Process a single uploaded document (legacy function for compatibility)"""
    process_multiple_documents([uploaded_file])


def display_batch_processing_results(batch_results):
    """Display batch processing results"""
    st.subheader("📊 Batch Processing Results")

    # Summary metrics
    successful_files = [r for r in batch_results if r['success']]
    failed_files = [r for r in batch_results if not r['success']]

    col1, col2, col3, col4 = st.columns(4)

    with col1:
        st.metric("Total Files", len(batch_results))

    with col2:
        st.metric("Successful", len(successful_files))

    with col3:
        st.metric("Failed", len(failed_files))

    with col4:
        total_chunks = sum(r['metadata']['num_chunks'] for r in successful_files if 'metadata' in r)
        st.metric("Total Chunks", total_chunks)

    # Detailed results
    if successful_files:
        st.subheader("✅ Successfully Processed Documents")
        for result in successful_files:
            with st.expander(f"📄 {result['filename']}"):
                if 'metadata' in result:
                    metadata = result['metadata']
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.write(f"**Text Length:** {metadata['total_text_length']:,} chars")
                    with col2:
                        st.write(f"**Chunks:** {metadata['num_chunks']}")
                    with col3:
                        avg_length = metadata['total_text_length'] // metadata['num_chunks'] if metadata['num_chunks'] > 0 else 0
                        st.write(f"**Avg Chunk:** {avg_length} chars")

    if failed_files:
        st.subheader("❌ Failed Documents")
        for result in failed_files:
            with st.expander(f"📄 {result['filename']} - Error"):
                st.error(f"Error: {result.get('error', 'Unknown error')}")


def display_processing_results(document):
    """Display single document processing results (legacy function)"""
    display_batch_processing_results([{
        'filename': 'Document',
        'success': True,
        'metadata': document
    }])


def query_interface():
    """Handle user queries and cross-document search with smart suggestions"""
    st.subheader("🔍 Cross-Document Query Interface")

    if not st.session_state.processed_documents:
        st.warning("⚠️ Please upload and process documents first.")
        # Show example prompts even without documents
        st.markdown("---")
        display_suggested_prompts({})
        return

    # Display document corpus info
    total_docs = len(st.session_state.processed_documents)
    total_chunks = sum(doc['metadata']['num_chunks'] for doc in st.session_state.processed_documents.values())

    st.info(f"📚 Knowledge Base: {total_docs} documents with {total_chunks} total chunks available for search")

    # Smart suggested prompts section
    st.markdown("---")
    selected_prompt = display_suggested_prompts(st.session_state.processed_documents)

    # Query input with auto-population from suggested prompts
    default_query = ""
    if selected_prompt:
        default_query = selected_prompt
    elif hasattr(st.session_state, 'selected_prompt'):
        default_query = st.session_state.selected_prompt

    query = st.text_input(
        "Enter your question about the clinical trials:",
        value=default_query,
        placeholder="e.g., What were the primary endpoints across these studies?",
        help="Ask questions that will search across all uploaded documents simultaneously"
    )

    # Clear the selected prompt after use
    if selected_prompt and 'selected_prompt' in st.session_state:
        del st.session_state.selected_prompt

    # Advanced settings
    with st.expander("🔧 Advanced Settings"):
        col1, col2 = st.columns(2)
        with col1:
            top_k = st.slider("Number of relevant chunks to retrieve", 1, 20, 8)
            temperature = st.slider("Response creativity (temperature)", 0.0, 1.0, 0.3, 0.1)
            score_threshold = st.slider("Similarity threshold", 0.0, 1.0, 0.0, 0.1)
        with col2:
            max_tokens = st.slider("Maximum response length", 256, 2048, 1024, 128)

            # Document filtering option
            st.write("**Document Filtering (Optional)**")
            available_docs = list(st.session_state.processed_documents.items())
            doc_names = [f"{doc_data['filename']}" for doc_id, doc_data in available_docs]

            selected_docs = st.multiselect(
                "Filter by specific documents:",
                options=range(len(available_docs)),
                format_func=lambda x: doc_names[x],
                help="Leave empty to search all documents"
            )

    # Process query
    if st.button("🚀 Search All Documents", type="primary") and query:
        # Get selected document IDs for filtering
        document_filter = None
        if selected_docs:
            document_filter = [available_docs[i][0] for i in selected_docs]

        process_cross_document_query(query, top_k, temperature, max_tokens, score_threshold, document_filter)


def process_cross_document_query(query: str, top_k: int, temperature: float, max_tokens: int,
                               score_threshold: float, document_filter: List[str] = None):
    """Process cross-document query and generate response"""
    try:
        with st.spinner("Searching across all documents..."):
            # Initialize components
            embedding_gen = get_embedding_generator()
            vector_db = get_vector_database()
            groq_client = get_groq_client()

            # Generate query embedding
            query_embedding = embedding_gen.generate_query_embedding(query)

            # Search for similar chunks across all documents
            similar_chunks = vector_db.search_similar_chunks(
                query_embedding.tolist(),
                top_k=top_k,
                score_threshold=score_threshold,
                document_filter=document_filter
            )

            if not similar_chunks:
                st.warning("⚠️ No relevant information found for your query. Try rephrasing or lowering the similarity threshold.")
                return

            # Display search summary
            unique_docs = set(chunk['filename'] for chunk in similar_chunks)
            st.success(f"🔍 Found {len(similar_chunks)} relevant chunks from {len(unique_docs)} document(s)")

            # Display retrieved chunks with source attribution
            st.subheader("📚 Retrieved Relevant Information")

            # Group chunks by document for better organization
            chunks_by_doc = {}
            for chunk in similar_chunks:
                doc_name = chunk['filename']
                if doc_name not in chunks_by_doc:
                    chunks_by_doc[doc_name] = []
                chunks_by_doc[doc_name].append(chunk)

            # Display chunks organized by document
            for doc_name, doc_chunks in chunks_by_doc.items():
                st.write(f"**📄 From: {doc_name}** ({len(doc_chunks)} chunks)")

                for i, chunk in enumerate(doc_chunks):
                    chunk_index = similar_chunks.index(chunk) + 1
                    with st.expander(f"Chunk {chunk_index} - Similarity: {chunk['score']:.3f}"):
                        st.markdown(f'<div class="chunk-display">{chunk["text"]}</div>', unsafe_allow_html=True)

                        # Enhanced metadata display
                        col1, col2, col3 = st.columns(3)
                        with col1:
                            st.caption(f"📄 **Source:** {chunk['filename']}")
                        with col2:
                            st.caption(f"📏 **Length:** {chunk['length']} chars")
                        with col3:
                            st.caption(f"🎯 **Score:** {chunk['score']:.3f}")

                st.markdown("---")

            # Generate response
            st.subheader("🤖 AI Response")
            with st.spinner("Generating cross-document response..."):
                response = groq_client.generate_response(
                    query,
                    similar_chunks,
                    temperature=temperature,
                    max_tokens=max_tokens
                )

                st.markdown(f"**Query:** {query}")
                st.markdown("**Answer:**")
                st.markdown(response)

                # Enhanced query history with cross-document info
                st.session_state.query_history.append({
                    'query': query,
                    'response': response,
                    'chunks_used': len(similar_chunks),
                    'documents_searched': len(unique_docs),
                    'source_documents': list(unique_docs),
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'cross_document': True
                })

    except Exception as e:
        st.error(f"❌ Error processing cross-document query: {str(e)}")


def process_query(query: str, top_k: int, temperature: float, max_tokens: int, score_threshold: float):
    """Legacy single document query processing (redirects to cross-document search)"""
    process_cross_document_query(query, top_k, temperature, max_tokens, score_threshold)


def sidebar_info():
    """Display enhanced sidebar information for cross-document system"""
    with st.sidebar:
        st.header("📋 Knowledge Base Status")

        # Document corpus overview
        st.subheader("📚 Document Corpus")
        if st.session_state.processed_documents:
            total_docs = len(st.session_state.processed_documents)
            total_chunks = sum(doc['metadata']['num_chunks'] for doc in st.session_state.processed_documents.values())
            total_chars = sum(doc['metadata']['total_text_length'] for doc in st.session_state.processed_documents.values())

            # Summary metrics
            col1, col2 = st.columns(2)
            with col1:
                st.metric("Documents", total_docs)
                st.metric("Total Chunks", total_chunks)
            with col2:
                st.metric("Avg Chunks/Doc", f"{total_chunks//total_docs if total_docs > 0 else 0}")
                st.metric("Total Text", f"{total_chars//1000}K chars")

            # Individual document details
            with st.expander("📄 Document Details"):
                for doc_id, doc_data in st.session_state.processed_documents.items():
                    st.write(f"**{doc_data['filename']}**")
                    st.caption(f"  • Chunks: {doc_data['metadata']['num_chunks']}")
                    st.caption(f"  • Length: {doc_data['metadata']['total_text_length']:,} chars")
                    st.caption(f"  • Processed: {doc_data['metadata']['processed_at']}")
                    st.markdown("---")
        else:
            st.write("No documents in knowledge base yet")
            st.info("Upload documents to build your knowledge base")

        st.markdown("---")

        # Enhanced query history
        st.subheader("🔍 Query History")
        if st.session_state.query_history:
            for i, query_data in enumerate(reversed(st.session_state.query_history[-5:])):
                query_num = len(st.session_state.query_history) - i
                with st.expander(f"Query {query_num}"):
                    st.write(f"**Q:** {query_data['query'][:80]}...")

                    # Enhanced query info
                    if query_data.get('cross_document', False):
                        st.caption(f"🔍 Cross-document search")
                        st.caption(f"📄 {query_data.get('documents_searched', 0)} docs searched")
                        st.caption(f"📝 {query_data.get('chunks_used', 0)} chunks retrieved")
                    else:
                        st.caption(f"📝 {query_data.get('chunks_used', 0)} chunks used")

                    st.caption(f"⏰ {query_data['timestamp']}")
        else:
            st.write("No queries yet")

        st.markdown("---")

        # System configuration
        st.subheader("⚙️ System Configuration")
        st.write("• **Search Mode:** Cross-Document")
        st.write("• **Embedding Model:** all-MiniLM-L6-v2")
        st.write("• **LLM Model:** Llama-4-Scout-17B")
        st.write("• **Vector DB:** Qdrant (Local)")
        st.write("• **Chunking:** Paragraph-based")
        st.write("• **Max Documents:** 10")

        # Performance info
        if st.session_state.processed_documents:
            st.markdown("---")
            st.subheader("📊 Performance")
            st.caption("✅ Knowledge base ready")
            st.caption("🚀 Cross-document search enabled")
            st.caption("🔍 Unified vector storage active")


def main():
    """Main application function for cross-document RAG system"""
    # Configure page
    st.set_page_config(
        page_title="Clinical Trial RAG System",
        page_icon="🧬",
        layout="wide",
        initial_sidebar_state="expanded"
    )

    # Load custom CSS
    load_css()

    # Initialize session state
    initialize_session_state()

    # Display professional header
    display_header()

    # Main content area with professional spacing
    st.markdown('<div class="fade-in">', unsafe_allow_html=True)

    col1, col2 = st.columns([2, 1], gap="large")

    with col1:
        # Batch document upload section
        st.markdown('<div class="upload-section slide-up">', unsafe_allow_html=True)
        upload_and_process_documents()
        st.markdown('</div>', unsafe_allow_html=True)

        st.markdown("<br>", unsafe_allow_html=True)

        # Cross-document query interface
        query_interface()

    with col2:
        # Enhanced sidebar information
        sidebar_info()

    st.markdown('</div>', unsafe_allow_html=True)


if __name__ == "__main__":
    main()