"""
GROQ API Integration Module for Clinical Trial RAG Application
Implements few-shot prompting for clinical trial queries
"""

from groq import Groq
import os
from typing import List, Dict, Optional
import streamlit as st
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class GroqLLMClient:
    """Handles GROQ API integration with few-shot prompting"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize GROQ client
        
        Args:
            api_key: GROQ API key (optional, will use env var if not provided)
        """
        self.api_key = api_key or os.getenv("GROQ_API_KEY")
        self.client = None
        self.model_name = "meta-llama/llama-4-scout-17b-16e-instruct"
    
    def get_client(self):
        """Get or create GROQ client"""
        if self.client is None:
            if not self.api_key:
                raise Exception("GROQ API key not found. Please set GROQ_API_KEY environment variable.")
            self.client = Groq(api_key=self.api_key)
        return self.client
    
    def create_few_shot_prompt(self, query: str, context_chunks: List[Dict]) -> str:
        """
        Create a few-shot prompt for clinical trial queries
        
        Args:
            query: User query
            context_chunks: Retrieved relevant document chunks
            
        Returns:
            str: Formatted prompt with few-shot examples
        """
        # Enhanced few-shot examples for clinical trial queries
        few_shot_examples = """
Example 1:
Context: "The primary endpoint was the change from baseline in HbA1c at 24 weeks. Secondary endpoints included changes in fasting plasma glucose, body weight, and safety parameters. The study was powered to detect a 0.5% difference in HbA1c with 90% power."
Query: What were the primary endpoints of the study?
Answer: The primary endpoint of this study was the change from baseline in HbA1c (glycated hemoglobin) levels measured at 24 weeks. This is a standard measure of long-term blood glucose control in diabetes studies, representing average blood glucose over 2-3 months. The study was adequately powered to detect a clinically meaningful 0.5% difference in HbA1c with 90% statistical power.

Example 2:
Context: "The most common adverse events were nausea (15% vs 8% placebo), headache (12% vs 10% placebo), and diarrhea (8% vs 3% placebo). One serious adverse event of pancreatitis was reported in the treatment group. The overall discontinuation rate due to adverse events was 3.2% in the treatment group vs 1.8% in placebo."
Query: What adverse events were reported?
Answer: The study reported several adverse events with clear differences between treatment and placebo groups. The most frequent were nausea (15% treatment vs 8% placebo), headache (12% vs 10%), and diarrhea (8% vs 3%). Notably, there was one serious adverse event of pancreatitis in the treatment group. The overall discontinuation rate due to adverse events was low but higher in the treatment group (3.2%) compared to placebo (1.8%).

Example 3:
Context: "A total of 450 patients were randomized, with 225 in each group. The completion rate was 89% (200/225) in the treatment group and 92% (207/225) in the placebo group. The most common reasons for discontinuation were adverse events (3.2% treatment, 1.8% placebo) and loss to follow-up (4.4% treatment, 3.6% placebo)."
Query: How many patients completed the study?
Answer: Out of 450 randomized patients (225 per group), the study demonstrated high completion rates: 89% (200 patients) in the treatment group and 92% (207 patients) in the placebo group. This indicates excellent patient retention. The primary reasons for discontinuation were adverse events (3.2% vs 1.8%) and loss to follow-up (4.4% vs 3.6%), with slightly higher rates in the treatment group.

Example 4:
Context: "The primary efficacy analysis showed HbA1c reduction of -1.2% (95% CI: -1.4, -1.0) for high-dose, -0.8% (95% CI: -1.0, -0.6) for low-dose, and -0.2% (95% CI: -0.4, 0.0) for placebo. Response rates (HbA1c <7%) were 65%, 45%, and 15% respectively (p<0.001 for both doses vs placebo)."
Query: Compare the efficacy results across different treatment arms.
Answer: The efficacy results demonstrated a clear dose-response relationship. The high-dose treatment achieved the greatest HbA1c reduction (-1.2%, 95% CI: -1.4 to -1.0), followed by low-dose (-0.8%, 95% CI: -1.0 to -0.6), and placebo (-0.2%, 95% CI: -0.4 to 0.0). Target achievement rates (HbA1c <7%) were correspondingly higher: 65% for high-dose, 45% for low-dose, and 15% for placebo, with both active treatments significantly superior to placebo (p<0.001).

Example 5:
Context: "This was a randomized, double-blind, placebo-controlled, parallel-group study conducted at 45 centers across 8 countries. Patients aged 18-75 with type 2 diabetes and HbA1c 7.0-10.5% on stable metformin therapy were eligible. Key exclusion criteria included severe renal impairment (eGFR <30), history of pancreatitis, and recent cardiovascular events."
Query: What was the study design and patient population?
Answer: This was a robust randomized, double-blind, placebo-controlled, parallel-group study conducted across 45 centers in 8 countries. The study population consisted of adults aged 18-75 years with type 2 diabetes, inadequately controlled (HbA1c 7.0-10.5%) despite stable metformin therapy. Important exclusion criteria included severe renal impairment (eGFR <30 mL/min/1.73m²), history of pancreatitis, and recent cardiovascular events, ensuring a well-defined and appropriate patient population for the intervention.
"""
        
        # Format context from retrieved chunks
        context_text = "\n\n".join([
            f"Chunk {i+1}: {chunk['text'][:500]}..." if len(chunk['text']) > 500 
            else f"Chunk {i+1}: {chunk['text']}"
            for i, chunk in enumerate(context_chunks)
        ])
        
        # Create the enhanced full prompt
        prompt = f"""You are an expert clinical trial research assistant with deep knowledge of clinical research methodology, regulatory requirements, and statistical analysis. Your role is to provide accurate, comprehensive, and clinically relevant answers based on clinical trial documents.

Key guidelines for your responses:
1. Always cite specific data points with numbers, percentages, and confidence intervals when available
2. Explain clinical significance and context for non-expert readers
3. Highlight important safety considerations and regulatory implications
4. Compare treatment arms when multiple groups are present
5. Provide clear, structured answers that address the specific question asked
6. When analyzing cross-document information, clearly indicate which document each finding comes from

{few_shot_examples}

Now, based on the following context from clinical trial documents, please answer the user's query following the guidelines above:

Context:
{context_text}

Query: {query}

Answer: """
        
        return prompt
    
    def generate_response(self, query: str, context_chunks: List[Dict], 
                         temperature: float = 0.3, max_tokens: int = 1024) -> str:
        """
        Generate response using GROQ API with few-shot prompting
        
        Args:
            query: User query
            context_chunks: Retrieved relevant document chunks
            temperature: Sampling temperature
            max_tokens: Maximum tokens in response
            
        Returns:
            str: Generated response
        """
        try:
            client = self.get_client()
            
            # Create few-shot prompt
            prompt = self.create_few_shot_prompt(query, context_chunks)
            
            # Generate response
            completion = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=temperature,
                max_completion_tokens=max_tokens,
                top_p=1,
                stream=False,
                stop=None,
            )
            
            return completion.choices[0].message.content
            
        except Exception as e:
            raise Exception(f"Error generating response: {str(e)}")
    
    def generate_streaming_response(self, query: str, context_chunks: List[Dict], 
                                  temperature: float = 0.3, max_tokens: int = 1024):
        """
        Generate streaming response using GROQ API
        
        Args:
            query: User query
            context_chunks: Retrieved relevant document chunks
            temperature: Sampling temperature
            max_tokens: Maximum tokens in response
            
        Yields:
            str: Response chunks
        """
        try:
            client = self.get_client()
            
            # Create few-shot prompt
            prompt = self.create_few_shot_prompt(query, context_chunks)
            
            # Generate streaming response
            completion = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=temperature,
                max_completion_tokens=max_tokens,
                top_p=1,
                stream=True,
                stop=None,
            )
            
            for chunk in completion:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            raise Exception(f"Error generating streaming response: {str(e)}")
    
    def summarize_document(self, chunks: List[Dict], max_chunks: int = 10) -> str:
        """
        Generate a summary of the document based on its chunks
        
        Args:
            chunks: Document chunks
            max_chunks: Maximum number of chunks to use for summary
            
        Returns:
            str: Document summary
        """
        try:
            # Use first few chunks for summary
            summary_chunks = chunks[:max_chunks]
            
            # Create summary prompt
            context_text = "\n\n".join([chunk['text'] for chunk in summary_chunks])
            
            prompt = f"""Please provide a comprehensive summary of this clinical trial document. Focus on:
1. Study objectives and design
2. Patient population
3. Treatment protocols
4. Key findings and results
5. Safety information

Document content:
{context_text}

Summary:"""
            
            client = self.get_client()
            
            completion = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_completion_tokens=1024,
                top_p=1,
                stream=False,
                stop=None,
            )
            
            return completion.choices[0].message.content
            
        except Exception as e:
            raise Exception(f"Error generating summary: {str(e)}")


# Global GROQ client instance
@st.cache_resource
def get_groq_client():
    """Get cached GROQ client instance"""
    return GroqLLMClient()
