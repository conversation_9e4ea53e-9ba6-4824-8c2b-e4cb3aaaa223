"""
GROQ API Integration Module for Clinical Trial RAG Application
Implements few-shot prompting for clinical trial queries
"""

from groq import Groq
import os
from typing import List, Dict, Optional
import streamlit as st
from dotenv import load_dotenv

# Load environment variables
load_dotenv()


class GroqLLMClient:
    """Handles GROQ API integration with few-shot prompting"""
    
    def __init__(self, api_key: Optional[str] = None):
        """
        Initialize GROQ client
        
        Args:
            api_key: GROQ API key (optional, will use env var if not provided)
        """
        self.api_key = api_key or os.getenv("GROQ_API_KEY")
        self.client = None
        self.model_name = "meta-llama/llama-4-scout-17b-16e-instruct"
    
    def get_client(self):
        """Get or create GROQ client"""
        if self.client is None:
            if not self.api_key:
                raise Exception("GROQ API key not found. Please set GROQ_API_KEY environment variable.")
            self.client = Groq(api_key=self.api_key)
        return self.client
    
    def create_few_shot_prompt(self, query: str, context_chunks: List[Dict]) -> str:
        """
        Create a few-shot prompt for clinical trial queries
        
        Args:
            query: User query
            context_chunks: Retrieved relevant document chunks
            
        Returns:
            str: Formatted prompt with few-shot examples
        """
        # Few-shot examples for clinical trial queries
        few_shot_examples = """
Example 1:
Context: "The primary endpoint was overall survival (OS). Secondary endpoints included progression-free survival (PFS), objective response rate (ORR), and safety. The study enrolled 450 patients with advanced melanoma."
Query: What were the study endpoints?
Answer: The study had multiple endpoints:
- Primary endpoint: Overall survival (OS)
- Secondary endpoints: Progression-free survival (PFS), objective response rate (ORR), and safety assessments

Example 2:
Context: "Patients received either Drug A 200mg daily (n=225) or placebo (n=225) in a randomized, double-blind fashion. The median age was 65 years (range 45-80)."
Query: What was the treatment protocol?
Answer: The treatment protocol involved:
- Drug A group: 200mg daily (225 patients)
- Control group: Placebo (225 patients)
- Study design: Randomized, double-blind
- Patient demographics: Median age 65 years (range 45-80)

Example 3:
Context: "Grade 3-4 adverse events occurred in 35% of patients in the treatment arm versus 20% in the control arm. The most common severe adverse events were neutropenia (15%) and fatigue (12%)."
Query: What were the safety results?
Answer: Safety results showed:
- Grade 3-4 adverse events: 35% (treatment) vs 20% (control)
- Most common severe adverse events:
  - Neutropenia: 15%
  - Fatigue: 12%
"""
        
        # Format context from retrieved chunks
        context_text = "\n\n".join([
            f"Chunk {i+1}: {chunk['text'][:500]}..." if len(chunk['text']) > 500 
            else f"Chunk {i+1}: {chunk['text']}"
            for i, chunk in enumerate(context_chunks)
        ])
        
        # Create the full prompt
        prompt = f"""You are a clinical trial research assistant. Use the provided context to answer questions about clinical trials accurately and comprehensively.

{few_shot_examples}

Now, based on the following context from clinical trial documents, please answer the user's query:

Context:
{context_text}

Query: {query}

Answer: """
        
        return prompt
    
    def generate_response(self, query: str, context_chunks: List[Dict], 
                         temperature: float = 0.3, max_tokens: int = 1024) -> str:
        """
        Generate response using GROQ API with few-shot prompting
        
        Args:
            query: User query
            context_chunks: Retrieved relevant document chunks
            temperature: Sampling temperature
            max_tokens: Maximum tokens in response
            
        Returns:
            str: Generated response
        """
        try:
            client = self.get_client()
            
            # Create few-shot prompt
            prompt = self.create_few_shot_prompt(query, context_chunks)
            
            # Generate response
            completion = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=temperature,
                max_completion_tokens=max_tokens,
                top_p=1,
                stream=False,
                stop=None,
            )
            
            return completion.choices[0].message.content
            
        except Exception as e:
            raise Exception(f"Error generating response: {str(e)}")
    
    def generate_streaming_response(self, query: str, context_chunks: List[Dict], 
                                  temperature: float = 0.3, max_tokens: int = 1024):
        """
        Generate streaming response using GROQ API
        
        Args:
            query: User query
            context_chunks: Retrieved relevant document chunks
            temperature: Sampling temperature
            max_tokens: Maximum tokens in response
            
        Yields:
            str: Response chunks
        """
        try:
            client = self.get_client()
            
            # Create few-shot prompt
            prompt = self.create_few_shot_prompt(query, context_chunks)
            
            # Generate streaming response
            completion = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=temperature,
                max_completion_tokens=max_tokens,
                top_p=1,
                stream=True,
                stop=None,
            )
            
            for chunk in completion:
                if chunk.choices[0].delta.content:
                    yield chunk.choices[0].delta.content
                    
        except Exception as e:
            raise Exception(f"Error generating streaming response: {str(e)}")
    
    def summarize_document(self, chunks: List[Dict], max_chunks: int = 10) -> str:
        """
        Generate a summary of the document based on its chunks
        
        Args:
            chunks: Document chunks
            max_chunks: Maximum number of chunks to use for summary
            
        Returns:
            str: Document summary
        """
        try:
            # Use first few chunks for summary
            summary_chunks = chunks[:max_chunks]
            
            # Create summary prompt
            context_text = "\n\n".join([chunk['text'] for chunk in summary_chunks])
            
            prompt = f"""Please provide a comprehensive summary of this clinical trial document. Focus on:
1. Study objectives and design
2. Patient population
3. Treatment protocols
4. Key findings and results
5. Safety information

Document content:
{context_text}

Summary:"""
            
            client = self.get_client()
            
            completion = client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_completion_tokens=1024,
                top_p=1,
                stream=False,
                stop=None,
            )
            
            return completion.choices[0].message.content
            
        except Exception as e:
            raise Exception(f"Error generating summary: {str(e)}")


# Global GROQ client instance
@st.cache_resource
def get_groq_client():
    """Get cached GROQ client instance"""
    return GroqLLMClient()
