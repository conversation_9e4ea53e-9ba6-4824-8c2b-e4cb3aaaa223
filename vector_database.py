"""
Vector Database Module for Clinical Trial RAG Application
Uses local vector storage with similarity search capabilities
"""

import numpy as np
import pickle
import os
import uuid
from typing import List, Dict, Optional
import streamlit as st
from sklearn.metrics.pairwise import cosine_similarity


class VectorDatabase:
    """Handles vector storage and retrieval using Qdrant"""
    
    def __init__(self, collection_name: str = "clinical_trials"):
        """
        Initialize the vector database
        
        Args:
            collection_name: Name of the Qdrant collection
        """
        self.collection_name = collection_name
        self.client = None
        self.embedding_dimension = 384  # Dimension for all-MiniLM-L6-v2
    
    def get_client(self):
        """Get or create Qdrant client"""
        if self.client is None:
            try:
                # Try to connect to local Qdrant instance
                self.client = QdrantClient(host="localhost", port=6333)
                # Test connection
                self.client.get_collections()
            except Exception:
                # Fall back to in-memory storage for demo purposes
                self.client = QdrantClient(":memory:")
        return self.client
    
    def create_collection(self):
        """Create collection if it doesn't exist"""
        try:
            client = self.get_client()
            
            # Check if collection exists
            collections = client.get_collections().collections
            collection_names = [col.name for col in collections]
            
            if self.collection_name not in collection_names:
                # Create collection with HNSW indexing
                client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config=VectorParams(
                        size=self.embedding_dimension,
                        distance=Distance.COSINE
                    )
                )
                st.success(f"Created collection: {self.collection_name}")
            
        except Exception as e:
            raise Exception(f"Error creating collection: {str(e)}")
    
    def store_document_chunks(self, chunks: List[Dict], document_id: str, 
                            filename: str) -> List[str]:
        """
        Store document chunks in the vector database
        
        Args:
            chunks: List of chunks with embeddings
            document_id: Unique document identifier
            filename: Original filename
            
        Returns:
            List[str]: List of stored point IDs
        """
        try:
            client = self.get_client()
            self.create_collection()
            
            points = []
            point_ids = []
            
            for chunk in chunks:
                if 'embedding' not in chunk:
                    continue
                
                point_id = str(uuid.uuid4())
                point_ids.append(point_id)
                
                # Create point with metadata
                point = PointStruct(
                    id=point_id,
                    vector=chunk['embedding'],
                    payload={
                        'document_id': document_id,
                        'filename': filename,
                        'chunk_id': chunk['id'],
                        'text': chunk['text'],
                        'length': chunk['length']
                    }
                )
                points.append(point)
            
            # Store points in batch
            if points:
                client.upsert(
                    collection_name=self.collection_name,
                    points=points
                )
            
            return point_ids
            
        except Exception as e:
            raise Exception(f"Error storing document chunks: {str(e)}")
    
    def search_similar_chunks(self, query_embedding: List[float], 
                            top_k: int = 5, score_threshold: float = 0.0) -> List[Dict]:
        """
        Search for similar chunks using vector similarity
        
        Args:
            query_embedding: Query embedding vector
            top_k: Number of top results to return
            score_threshold: Minimum similarity score threshold
            
        Returns:
            List[Dict]: Similar chunks with scores and metadata
        """
        try:
            client = self.get_client()
            
            # Perform vector search
            search_results = client.search(
                collection_name=self.collection_name,
                query_vector=query_embedding,
                limit=top_k,
                score_threshold=score_threshold
            )
            
            # Format results
            results = []
            for result in search_results:
                chunk_data = {
                    'id': result.id,
                    'score': result.score,
                    'text': result.payload['text'],
                    'document_id': result.payload['document_id'],
                    'filename': result.payload['filename'],
                    'chunk_id': result.payload['chunk_id'],
                    'length': result.payload['length']
                }
                results.append(chunk_data)
            
            return results
            
        except Exception as e:
            raise Exception(f"Error searching similar chunks: {str(e)}")
    
    def get_document_chunks(self, document_id: str) -> List[Dict]:
        """
        Get all chunks for a specific document
        
        Args:
            document_id: Document identifier
            
        Returns:
            List[Dict]: All chunks for the document
        """
        try:
            client = self.get_client()
            
            # Search with filter for specific document
            search_results = client.scroll(
                collection_name=self.collection_name,
                scroll_filter=Filter(
                    must=[
                        FieldCondition(
                            key="document_id",
                            match=MatchValue(value=document_id)
                        )
                    ]
                ),
                limit=1000  # Adjust based on expected document size
            )
            
            # Format results
            chunks = []
            for point in search_results[0]:  # scroll returns (points, next_page_offset)
                chunk_data = {
                    'id': point.id,
                    'text': point.payload['text'],
                    'chunk_id': point.payload['chunk_id'],
                    'length': point.payload['length']
                }
                chunks.append(chunk_data)
            
            return chunks
            
        except Exception as e:
            raise Exception(f"Error getting document chunks: {str(e)}")
    
    def delete_document(self, document_id: str) -> bool:
        """
        Delete all chunks for a specific document
        
        Args:
            document_id: Document identifier
            
        Returns:
            bool: True if successful
        """
        try:
            client = self.get_client()
            
            # Delete points with matching document_id
            client.delete(
                collection_name=self.collection_name,
                points_selector=Filter(
                    must=[
                        FieldCondition(
                            key="document_id",
                            match=MatchValue(value=document_id)
                        )
                    ]
                )
            )
            
            return True
            
        except Exception as e:
            raise Exception(f"Error deleting document: {str(e)}")
    
    def get_collection_info(self) -> Dict:
        """
        Get information about the collection
        
        Returns:
            Dict: Collection information
        """
        try:
            client = self.get_client()
            
            # Get collection info
            info = client.get_collection(self.collection_name)
            
            return {
                'name': info.config.params.vectors.size,
                'vectors_count': info.vectors_count,
                'points_count': info.points_count,
                'status': info.status
            }
            
        except Exception as e:
            return {'error': str(e)}


# Global vector database instance
@st.cache_resource
def get_vector_database():
    """Get cached vector database instance"""
    return VectorDatabase()
